<app-toolbar class="toolbar" (forFilter)="setToFilter($event)" (showTokensDate)="setTokesDates($event)"
  [activityCategory]="activityCategory" [activitiesList]="activitiesList"></app-toolbar>

<div class="row g-5 mb-5">
  <div class="col-xl-4">
    <div class="card h-xl-100">
      <app-activities-goal-widget class="card card-xl-stretch mb-xl-8" [activitiesList]="activitiesList"
        [currentSetting]="{sortBy,activityCategory}" chartColor="primary" chartHeight="165px">
      </app-activities-goal-widget>
    </div>
  </div>
  <div class="col-xl-8">
    <div class="card h-xl-100">
      <app-activities-chart-widget [tokensDate]="tokensDate" [activitiesList]="activitiesList"
        (categoriesActivity)="getActivityCategories($event)" class="card card-xl-stretch mb-xl-8">
      </app-activities-chart-widget>
    </div>
  </div>
</div>

<div class="d-flex flex-column flex-lg-row">
  <div class="flex-lg-row-fluid">
    <div class="d-flex flex-wrap flex-stack pb-7">
      <div class="d-flex flex-wrap align-items-center my-1">
        <p class="h3 fw-bolder me-5 my-1">
          {{ activitiesCount }} {{ activitiesCount === 1 ? "Log" : "Logs" }}
        </p>
        <div class="position-relative">
          <span class="svg-icon svg-icon-3 svg-icon-gray-500 position-absolute top-50 translate-middle ms-6">
            <app-svg-general svg="gen021"></app-svg-general>
          </span>
          <input type="text" class="form-control form-control-solid ps-10 bg-white rounded-2" placeholder="Search"
            [(ngModel)]="searchValue" (keyup)="onSearch()" />
        </div>
      </div>
      <div class="d-flex flex-wrap my-1">
        <div class="d-flex my-0">
          <button class="btn btn-light bg-white me-2" data-kt-menu-trigger="click" data-kt-menu-placement="right-end"
            data-kt-menu-flip="top-end" attr.data-kt-menu-static="{{ktMenuStatic}}">
            Filters {{ !!checkNumberOfFilters ? checkNumberOfFilters : '' }}
          </button>
          <app-activities-filter [list]="activitiesList" [searchValue]="searchValue"
            (isKtMenuStatic)="ktMenuStatic = $event.toString()" (filters)="applyFilter($event)">
          </app-activities-filter>
          <select class="form-select form-select-sm border-body bg-body w-150px me-5 drop-widh" [(ngModel)]="sortBy"
            (ngModelChange)="applyFilter(filters)">
            <option value="Recently Updated"> Recently Updated </option>
            <option value="Last Month"> Last Month </option>
            <option value="Last Quarter"> Last Quarter </option>
            <option value="Last Year"> Last Year </option>
          </select>
        </div>
      </div>
    </div>
    <div class="tab-content">
      <div class="tab-pane fade show active">
        <div class="card card-flush">
          <div class="card-body py-3">
            <div class="table-responsive">
              <table class="table table-row-bordered table-row-dashed gy-4 align-middle fw-bolder"
                aria-describedby="posts-table">
                <thead class="fs-7 text-gray-400 text-uppercase">
                  <tr>
                    <th class="min-w-400px"> Member & Log </th>
                    <th class="min-w-150px text-center"> Log Date </th>
                    <th class="min-w-100px text-center"> MVP Tokens </th>
                    <th class="min-w-150px text-center"> Type </th>
                    <th class="min-w-100px text-center"> Approval </th>
                  </tr>
                </thead>
                <tbody *ngIf="activitiesCount > 0">
                  <ng-container *ngFor="let activity of activitiesList | filter:filters:sortBy; index as i;">
                    <tr *ngIf="((i >= (getCountStartValue - 1)) && (i < getCountEndValue))">
                      <td class="fw-normal text-gray-800"
                        *ngIf="activity.moduleType!=='homework-submission' && activity.moduleType!=='update-profile' && activity.type!=='GAME' ">
                        <app-activity-item
                          [activity]="activity"
                          [index]="i"
                          [layout]="'table'"
                          [cityData]="cityData"
                          (profileNavigation)="profileNavigation($event.activity, $event.url)"
                          (navigateToOrganization)="navigateToOrganization($event)">
                        </app-activity-item>
                      </td>
                      <td class="fw-normal text-gray-800" *ngIf="activity.moduleType==='homework-submission'">
                        <app-activity-item
                          [activity]="activity"
                          [index]="i"
                          [layout]="'table'"
                          [cityData]="cityData"
                          (profileNavigation)="profileNavigation($event.activity, $event.url)"
                          (navigateToOrganization)="navigateToOrganization($event)">
                        </app-activity-item>
                      </td>
                      <td class="fw-normal text-gray-800" *ngIf="activity.moduleType==='update-profile'">
                        <app-activity-item
                          [activity]="activity"
                          [index]="i"
                          [layout]="'table'"
                          [cityData]="cityData"
                          (profileNavigation)="profileNavigation($event.activity, $event.url)"
                          (navigateToOrganization)="navigateToOrganization($event)">
                        </app-activity-item>
                      </td>
                      <td class="fw-normal text-gray-800" *ngIf="activity?.type==='GAME'">
                        <app-activity-item
                          [activity]="activity"
                          [index]="i"
                          [layout]="'table'"
                          [cityData]="cityData"
                          (profileNavigation)="profileNavigation($event.activity, $event.url)"
                          (navigateToOrganization)="navigateToOrganization($event)">
                        </app-activity-item>
                      </td>


                      <td class="fs-6 text-center"> <span>{{ activity.createdAt | date: 'MMM d, y' }}</span>
                        <p>{{ activity.createdAt | date: 'h:mm a' }}</p>
                      </td>
                      <td class="fs-6 text-center"> {{ activity?.activityTokens || '-'}} </td>
                      <td class="fs-6 text-center">
                        <button class="btn btn-secondary fw-medium text-hover-white" [ngClass]="{'btn-light-warning ': activity.activityType === 'MEMBERSHIP', 
                          'btn-light-success ': activity.activityType === 'PROJECT' || activity.activityType === 'FUNDING', 
                          'btn-light-danger ': activity.activityType === 'STORYTELLING', 
                          'btn-light-info ': ''
                          }">
                          {{activity?.activityType | titlecase}}
                        </button>
                      </td>
                      <td class="text-center w-225">
                        <button class="btn btn-secondary text-gray-500 fw-medium w-100"
                          *ngIf="activity?.requestStatus === 'SYSTEM_APPROVED' || activity?.requestStatus === null">
                          System Approved </button>
                        <button class="btn btn-primary fw-medium w-100"
                          *ngIf="activity?.requestStatus === 'ADMIN_APPROVED'"> Admin Approved </button>
                        <button class="btn btn-danger fw-medium w-100"
                          *ngIf="activity?.requestStatus === 'ADMIN_DENIED'"> Admin Denied </button>
                        <div class="modal-footer justify-content-center"
                          *ngIf="activity?.requestStatus === 'PENDING_APPROVAL'">
                          <button type="button" class="btn btn-primary" style="margin-right: 6px;"
                            (click)="updateActivity('ADMIN_APPROVED', activity)"> Approve </button>
                          <button type="button" class="btn btn-danger"
                            (click)="updateActivity('ADMIN_DENIED', activity)"> Decline </button>
                        </div>
                      </td>
                    </tr>
                  </ng-container>
                </tbody>
                <tbody class="fs-6" *ngIf="activitiesCount === 0">
                  <tr>
                    <td colspan="5" class="text-center">
                      <p class="text-gray-800 fw-bold mb-0"> No Records Found! </p>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
        <app-pagination [hidden]="activitiesCount === 0" [recordCount]="activitiesCount"></app-pagination>
      </div>
    </div>
  </div>
</div>