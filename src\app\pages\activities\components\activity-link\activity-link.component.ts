import { Component, Input, Output, EventEmitter } from '@angular/core';
import { Router } from '@angular/router';
import { SharedService } from '../../../../shared/services/shared.service';

@Component({
  selector: 'app-activity-link',
  templateUrl: './activity-link.component.html',
  styleUrls: ['./activity-link.component.scss']
})
export class ActivityLinkComponent {
  @Input() activity: any;
  @Input() linkText: string = '';
  @Input() cssClass: string = 'text-gray-800 text-hover-primary';
  @Input() useBreakClass: boolean = false;
  @Output() profileNavigation = new EventEmitter<{activity: any, url: string}>();

  constructor(
    private router: Router,
    private sharedService: SharedService
  ) {}

  get displayText(): string {
    return this.linkText || (this.activity?.moduleName || '');
  }

  get linkClasses(): string {
    return this.useBreakClass ? `${this.cssClass} text-break` : this.cssClass;
  }

  get routerLink(): string[] | null {
    if (!this.activity) return null;

    const { moduleType, moduleId, type, moduleUser } = this.activity;

    // Handle TOKENS type
    if (type === 'TOKENS') {
      return ['/activity-submission/view-submission', moduleId];
    }

    // Handle different module types
    switch (moduleType) {
      case 'business':
        return [`/${moduleType}es/view-${moduleType}`, moduleId];
      
      case 'member':
        return [`/${moduleType}s/view-${moduleType}`, this.sharedService.getEncryptedId(moduleId)];
      
      case 'story':
        return ['/assigment-submission/view-submission', moduleId];
      
      case 'homework':
        return ['/activities/view-activities/' + moduleId];
      
      case 'student':
        const userType = moduleUser?.isStakeholder ? 'stakeholder' : 'student';
        return [`/${userType}s/view-${userType}`, moduleId];
      
      case 'association':
        // Association uses click handler, not router link
        return null;
      
      case 'update-profile':
        // Update profile uses click handler, not router link
        return null;
      
      default:
        // Generic case for other module types
        if (this.shouldUseGenericRoute(moduleType)) {
          return [`/${moduleType}s/view-${moduleType}`, moduleId];
        }
        return null;
    }
  }

  get useClickHandler(): boolean {
    if (!this.activity) return false;
    
    const { moduleType } = this.activity;
    return moduleType === 'association' || moduleType === 'update-profile';
  }

  private shouldUseGenericRoute(moduleType: string): boolean {
    const excludedTypes = ['member', 'business', 'story', 'homework', 'update-profile', 'association'];
    return !excludedTypes.includes(moduleType);
  }

  onLinkClick(): void {
    if (!this.activity) return;

    const { moduleType, moduleId, moduleUser } = this.activity;

    if (moduleType === 'association') {
      this.sharedService.doNavigation(this.activity);
    } else if (moduleType === 'update-profile') {
      const userType = moduleUser?.isStakeholder ? 'stakeholder' : 'student';
      const url = `/${userType}s/view-${userType}/${moduleId}`;
      this.profileNavigation.emit({ activity: this.activity, url });
    }
  }
}
