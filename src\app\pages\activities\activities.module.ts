import { NgModule } from '@angular/core';
import { NgApexchartsModule } from 'ng-apexcharts';
import { NgxSpinnerModule } from 'ngx-spinner';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';

import { LayoutModule } from '../../_metronic/layout/layout.module';
import { ActivitiesRoutingModule } from './activities-routing.module';
import { FilterPipe } from './pipes/filter-pipe';
import { ActivitiesComponent } from './activities.component';
import { ActivitiesChartWidgetComponent } from './components/activities-chart-widget/activities-chart-widget.component';
import { ActivitiesFilterComponent } from './components/activities-filter/activities-filter.component';
import { ActivitiesGoalWidgetComponent } from './components/activities-goal-widget/activities-goal-widget.component';
import { ActivitiesWidgetComponent } from './components/activities-widget/activities-widget.component';
import { ActivityAvatarComponent } from './components/activity-avatar/activity-avatar.component';
import { ActivityLinkComponent } from './components/activity-link/activity-link.component';
import { ActivityContentComponent } from './components/activity-content/activity-content.component';
import { ActivityMetaComponent } from './components/activity-meta/activity-meta.component';
import { ActivityItemComponent } from './components/activity-item/activity-item.component';

@NgModule({
  imports: [
    ActivitiesRoutingModule,
    LayoutModule,
    MatFormFieldModule,
    MatInputModule,
    MatDatepickerModule,
    MatNativeDateModule,
    NgApexchartsModule,
    NgxSpinnerModule
  ],
  declarations: [
    ActivitiesComponent,
    ActivitiesWidgetComponent,
    ActivitiesFilterComponent,
    ActivitiesChartWidgetComponent,
    ActivitiesGoalWidgetComponent,
    ActivityAvatarComponent,
    ActivityLinkComponent,
    ActivityContentComponent,
    ActivityMetaComponent,
    ActivityItemComponent,
    FilterPipe,
  ],
  exports: [ActivitiesWidgetComponent],
  providers: [FilterPipe],
})
export class ActivitiesModule { }
