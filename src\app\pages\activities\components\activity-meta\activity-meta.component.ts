import { Component, Input } from '@angular/core';

@Component({
  selector: 'app-activity-meta',
  templateUrl: './activity-meta.component.html',
  styleUrls: ['./activity-meta.component.scss']
})
export class ActivityMetaComponent {
  @Input() activity: any;
  @Input() showTimestamp: boolean = false;
  @Input() timestampFormat: string = 'MMM d, y';
  @Input() timeFormat: string = 'h:mm a';
  @Input() layout: 'inline' | 'block' = 'block';

  get createdUserName(): string {
    return this.activity?.createdUserName || '';
  }

  get createdAt(): Date | null {
    return this.activity?.createdAt ? new Date(this.activity.createdAt) : null;
  }

  get metaClasses(): string {
    const baseClasses = 'fw-normal fs-6 text-gray-400';
    return this.layout === 'inline' ? `${baseClasses} d-inline` : baseClasses;
  }
}
