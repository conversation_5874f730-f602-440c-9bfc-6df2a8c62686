<div class="card-header border-0">
  <p class="h3 card-title align-items-start flex-column">
    <span class="card-label fw-bold text-dark">
      Recent Community Logs
    </span>
    <span class="text-muted mt-1 fw-semibold fs-7">
      Platform & system actions
    </span>
  </p>
  <div class="card-toolbar">
    <button type="button" class="btn btn-sm btn-icon btn-color-primary btn-active-light-primary"
      [routerLink]="['/logs']" aria-label="All Activities">
      <span class="svg-icon svg-icon-2">
        <app-svg-general svg="gen024"></app-svg-general>
      </span>
    </button>
  </div>
</div>
<div class="card-body pt-2 scroll-y mh-550px">
  <div class="timeline-label" *ngIf="activitiesList.length > 0">
    <div *ngFor="let activity of activitiesList; index as i">
      <ng-container
        *ngIf="activity?.type !== 'TOKENS' && activity.moduleType!=='homework-submission' && activity.moduleType!=='update-profile' && activity.moduleType !== 'fundTransactions' && activity.type !== 'GAME'">
        <app-activity-item
          [activity]="activity"
          [index]="i"
          [layout]="'timeline'"
          [cityData]="cityData"
          [showAvatar]="false"
          (profileNavigation)="profileNavigation($event.activity, $event.url)"
          (navigateToOrganization)="navigateToOrganization($event)">
        </app-activity-item>
      </ng-container>
      <ng-container *ngIf="activity?.type !== 'TOKENS' && activity.moduleType==='homework-submission'">
        <app-activity-item
          [activity]="activity"
          [index]="i"
          [layout]="'timeline'"
          [cityData]="cityData"
          [showAvatar]="false"
          (profileNavigation)="profileNavigation($event.activity, $event.url)"
          (navigateToOrganization)="navigateToOrganization($event)">
        </app-activity-item>
      </ng-container>
      <ng-container *ngIf="activity?.type !== 'TOKENS' && activity.moduleType==='update-profile'">
        <app-activity-item
          [activity]="activity"
          [index]="i"
          [layout]="'timeline'"
          [cityData]="cityData"
          [showAvatar]="false"
          (profileNavigation)="profileNavigation($event.activity, $event.url)"
          (navigateToOrganization)="navigateToOrganization($event)">
        </app-activity-item>
      </ng-container>
      <ng-container *ngIf="activity.moduleType==='fundTransactions' && activity?.type!=='TOKENS'">
        <app-activity-item
          [activity]="activity"
          [index]="i"
          [layout]="'timeline'"
          [cityData]="cityData"
          [showAvatar]="false"
          (profileNavigation)="profileNavigation($event.activity, $event.url)"
          (navigateToOrganization)="navigateToOrganization($event)">
        </app-activity-item>
      </ng-container>
      <ng-container *ngIf="activity?.type==='GAME'">
        <app-activity-item
          [activity]="activity"
          [index]="i"
          [layout]="'timeline'"
          [cityData]="cityData"
          [showAvatar]="false"
          (profileNavigation)="profileNavigation($event.activity, $event.url)"
          (navigateToOrganization)="navigateToOrganization($event)">
        </app-activity-item>
      </ng-container>
    </div>
  </div>
  <div class="d-flex justify-content-center align-items-center text-gray-700 fw-semibold fs-6"
    *ngIf="activitiesList.length === 0">
    No logs!
  </div>
</div>
<ngx-spinner name="dashboard_activity_spinner" [fullScreen]="false" type="ball-clip-rotate" bdColor="white"
  size="medium" color="#354029"></ngx-spinner>