<div class="card-header border-0">
  <p class="h3 card-title align-items-start flex-column">
    <span class="card-label fw-bold text-dark">
      Recent Community Logs
    </span>
    <span class="text-muted mt-1 fw-semibold fs-7">
      Platform & system actions
    </span>
  </p>
  <div class="card-toolbar">
    <button type="button" class="btn btn-sm btn-icon btn-color-primary btn-active-light-primary"
      [routerLink]="['/logs']" aria-label="All Activities">
      <span class="svg-icon svg-icon-2">
        <app-svg-general svg="gen024"></app-svg-general>
      </span>
    </button>
  </div>
</div>
<div class="card-body pt-2 scroll-y mh-550px">
  <div class="timeline-label" *ngIf="activitiesList.length > 0">
    <div class="timeline-item" *ngFor="let activity of activitiesList; index as i">
      <ng-container
        *ngIf="activity?.type !== 'TOKENS' && activity.moduleType!=='homework-submission' && activity.moduleType!=='update-profile' && activity.moduleType !== 'fundTransactions' && activity.type !== 'GAME'">
        <div class="timeline-label fw-bolder text-gray-800 fs-6">
          {{ activity.createdAt | date: 'h:mm a' }}
        </div>
        <div class="timeline-badge">
          <em class="fa fa-genderless fs-1"
            [ngClass]="{'text-warning': activity.moduleType === 'member', 'text-success': activity.moduleType === 'project', 'text-danger': activity.moduleType === 'project', 'text-info': activity.moduleType === 'post'}"></em>
        </div>
        <div class="timeline-content fw-normal text-gray-800 ps-3"
          *ngIf="(!activity.relatedTo && !(activity.moduleType === 'project' && activity.isRelationship) || activity.type === 'DELETED' &&  activity.relatedId )">
          <strong>
            <a *ngIf="activity.moduleType === 'business'"
              [routerLink]="['/' + activity.moduleType + 'es/view-' + activity.moduleType, activity.moduleId]"
              class="text-gray-800 text-hover-primary text-break">
              {{ activity.moduleName | titlecase }}
            </a>
            <a *ngIf="(activity.moduleType !=='member' && activity.moduleType !=='business' && activity.moduleType !=='member' && activity.moduleType !== 'story' &&activity.moduleType !== 'homework') && activity.moduleType !=='association'"
              [routerLink]="['/' + (activity.moduleType==='student' && activity.moduleUser?.isStakeholder?'stakeholder':activity.moduleType) + 's/view-' + (activity.moduleType==='student' && activity.moduleUser?.isStakeholder?'stakeholder':activity.moduleType), activity.moduleId]"
              class="text-gray-800 text-hover-primary">
              {{ activity.moduleName | titlecase }}
            </a>
            <a *ngIf="(activity.moduleType === 'member' )"
              [routerLink]="['/' + activity.moduleType + 's/view-' + activity.moduleType, sharedService.getEncryptedId(activity.moduleId)]"
              class="text-gray-800 text-hover-primary">
              {{ activity.moduleName | titlecase }}
            </a>
            <a *ngIf="(activity.moduleType ==='story' )"
              [routerLink]="['/activity-submission/view-submission', activity.moduleId]"
              class="text-gray-800 text-hover-primary">
              {{ activity.moduleName | titlecase }}
            </a>
            <a *ngIf="(activity.moduleType !=='member' && activity.moduleType === 'association') && activity.moduleType !== 'business'"
              (click)="sharedService.doNavigation(activity)" class="text-gray-800 text-hover-primary">
              {{ activity.moduleName | titlecase }}
            </a>
            <a *ngIf="(activity.moduleType ==='homework')"
              [routerLink]="['/activities/view-activities/' +activity.moduleId]"
              class="text-gray-800 text-hover-primary">
              {{ activity.moduleName | titlecase }}
            </a>

            <a *ngIf="(activity.type === 'TOKENS')"
              [routerLink]="['/activity-submission/view-submission', activity.moduleId]"
              class="text-gray-800 text-hover-primary">
              {{ activity.moduleName | titlecase }}
            </a>

          </strong>
          has been
          <strong
            *ngIf="(!activity.isRelationship && !activity.relatedTo) || (activity.moduleType!=='association' && activity.type === 'DELETED')">
            {{
            activity.type | lowercase }} </strong>
          <span *ngIf="activity.isRelationship">
            <span [innerHTML]="activity.type === 'UPDATED' ? '<strong> assigned new role </strong> in '
            : activity.type === 'ADDED' ? '<strong> enrolled </strong> to '
            : (activity.moduleType==='association' && activity.type === 'DELETED')?'<strong> disassociated </strong> from ': '<strong>
              discharged </strong> from '">
            </span>
            <strong>
              <a [routerLink]="['/' + activity.relatedTo + 's/view-' + activity.relatedTo, activity.relatedId]"
                class="text-gray-800 text-hover-primary text-break">
                {{ activity.relatedName | titlecase }}
              </a>
            </strong>
          </span>
          by
          <strong> {{ activity.createdUserName | titlecase }}</strong>.
        </div>
        <div class="timeline-content fw-normal text-gray-800 ps-3"
          *ngIf="(activity.relatedTo && activity.relatedTo !== activity.moduleType && !(activity.isRelationship && activity.moduleType === 'project') && !(activity.type === 'DELETED' &&  activity.relatedId)  && (activity?.type !== 'VIEWED' && activity?.type !== 'TOKENS'))">
          Relationship between
          <strong>
            <a (click)="sharedService.doNavigation(activity,'relatedName')"
              class="text-gray-800 text-hover-primary text-break">
              {{ activity.relatedName | titlecase }}
            </a>
          </strong>
          &
          <strong>
            <a (click)="sharedService.doNavigation(activity, 'moduleName')"
              class="text-gray-800 text-hover-primary text-break">
              {{ activity.moduleName | titlecase }}
            </a>
          </strong>
          has been
          <strong> {{ activity.type | lowercase }} </strong>
          by
          <strong> {{ activity.createdUserName | titlecase }}</strong>.
        </div>
        <div class="timeline-content fw-normal text-gray-800 ps-3"
          *ngIf="(activity.moduleType === 'project' && activity.isRelationship) && activity?.type !== 'TOKENS'">
          <strong>
            <a [routerLink]="['/' + activity.moduleType + 's/view-' + activity.moduleType, activity.moduleId]"
              class="text-gray-800 text-hover-primary text-break">
              {{ activity.moduleName | titlecase }}
            </a>
          </strong>
          has been
          <strong> {{ activity.type === 'ADDED' ? 'scheduled' : 'cancelled' }} </strong>
          for
          <strong>
            <a *ngIf="activity?.relatedTo === 'member'" (click)="navigateToOrganization(activity)"
              class="text-gray-800 text-hover-primary">
              {{ activity.relatedName | titlecase }}
            </a>
            <a *ngIf="activity?.relatedTo !== 'member'"
              [routerLink]="['/' + activity.relatedTo + 's/view-' + activity.relatedTo, activity.relatedId]"
              class="text-gray-800 text-hover-primary text-break">
              {{ activity.relatedName | titlecase }}
            </a>
          </strong>
          by
          <strong> {{ activity.createdUserName | titlecase }}</strong>.
        </div>

        <ng-container *ngIf="activity.type === 'VIEWED'">
          <div class="timeline-content fw-normal text-gray-800 ps-3">
            <strong>
              <a [routerLink]="['/activity-submission/view-submission/' + activity.moduleId]"
                class="text-gray-800 text-hover-primary">
                {{ activity.moduleName | titlecase }}
              </a>
            </strong>
            has been
            <strong> viewed </strong>
            by
            <strong> {{ activity.createdUserName | titlecase }}</strong>.
          </div>
        </ng-container>

        <ng-container *ngIf="activity.type === 'TOKENS'">
          <div class="timeline-content fw-normal text-gray-800 ps-3">
            <strong>
              <label class="text-gray-800" for="">
                {{activity?.activityTokens}} tokens
              </label>
            </strong>
            has been
            <strong> Added </strong>
            to
            <strong *ngIf="activity?.relatedTo !== 'member'"> <a class="text-gray-800 text-hover-primary"
                [routerLink]="['/' + activity.relatedTo + 's/view-' + activity.relatedTo, activity.relatedId]">{{activity?.relatedName}}</a></strong>
            <strong *ngIf="activity?.relatedTo === 'member'"> <a class="text-gray-800 text-hover-primary"
                [routerLink]="['/' + activity.relatedTo + 's/view-' + activity.relatedTo, sharedService.getEncryptedId(activity.relatedId)]">
                {{activity?.relatedName}}</a></strong>
            by
            <strong> {{ activity.createdUserName | titlecase }}</strong>.
          </div>
        </ng-container>
      </ng-container>
      <ng-container *ngIf="activity?.type !== 'TOKENS' && activity.moduleType==='homework-submission'">
        <div class="d-flex align-items-center">
          <div class="timeline-label fw-bolder text-gray-800 fs-6">
            {{ activity.createdAt | date: 'h:mm a' }}
          </div>
          <div class="timeline-badge">
            <em class="fa fa-genderless fs-1"
              [ngClass]="{'text-warning': activity.moduleType === 'member', 'text-success': activity.moduleType === 'project', 'text-danger': activity.moduleType === 'project', 'text-info': activity.moduleType === 'post'}"></em>
          </div>
          <div class="timeline-content fw-normal text-gray-800 ps-3">
            <strong><a class="text-gray-800 text-hover-primary text-break"
                (click)="sharedService.doNavigation(activity,'relatedName')">{{activity?.relatedName
                || '
                '}}</a></strong>
            has
            submitted their homework <strong><a
                [routerLink]="['/' + activity.moduleType +'/view-submission', activity.moduleId]"
                class="text-gray-800 text-hover-primary text-break">{{activity.moduleName}}</a></strong>.
            <br />

            <div class="fw-normal fs-6 text-gray-400">
              By <strong> {{ activity.createdUserName }} </strong>
            </div>
          </div>
        </div>
      </ng-container>
      <ng-container *ngIf="activity?.type !== 'TOKENS' && activity.moduleType==='update-profile'">
        <div class="d-flex align-items-center" *ngIf="activity?.moduleUser?.isStakeholder">
          <div class="timeline-label fw-bolder text-gray-800 fs-6">
            {{ activity.createdAt | date: 'h:mm a' }}
          </div>
          <div class="timeline-badge">
            <em class="fa fa-genderless fs-1"
              [ngClass]="{'text-warning': activity.moduleType === 'member', 'text-success': activity.moduleType === 'project', 'text-danger': activity.moduleType === 'project', 'text-info': activity.moduleType === 'post'}"></em>
          </div>
          <div class="timeline-content fw-normal text-gray-800 ps-3">
            <strong><a class="text-gray-800 text-hover-primary text-break"
                (click)="profileNavigation(activity,'/stakeholders/view-stakeholder/' +activity.moduleId)">{{activity?.moduleName
                || '
                '}}</a></strong>
            has
            requested <strong><a
                (click)="profileNavigation(activity,'/stakeholders/view-stakeholder/' +activity.moduleId)"
                class="text-gray-800 text-hover-primary text-break">{{activity.relatedName}}</a></strong>.
            <br />

            <div class="fw-normal fs-6 text-gray-400">
              By <strong> {{ activity.createdUserName }} </strong>
            </div>
          </div>
        </div>
        <div class="d-flex align-items-center" *ngIf="!activity?.moduleUser?.isStakeholder">
          <div class="timeline-label fw-bolder text-gray-800 fs-6">
            {{ activity.createdAt | date: 'h:mm a' }}
          </div>
          <div class="timeline-badge">
            <em class="fa fa-genderless fs-1"
              [ngClass]="{'text-warning': activity.moduleType === 'member', 'text-success': activity.moduleType === 'project', 'text-danger': activity.moduleType === 'project', 'text-info': activity.moduleType === 'post'}"></em>
          </div>
          <div class="timeline-content fw-normal text-gray-800 ps-3">
            <strong><a class="text-gray-800 text-hover-primary text-break"
                (click)="profileNavigation(activity,'/students/view-student/' +activity.moduleId)">{{activity?.moduleName
                || '
                '}}</a></strong>
            has
            requested <strong><a (click)="profileNavigation(activity,'/students/view-student/' +activity.moduleId)"
                class="text-gray-800 text-hover-primary text-break">{{activity.relatedName}}</a></strong>.
            <br />

            <div class="fw-normal fs-6 text-gray-400">
              By <strong> {{ activity.createdUserName }} </strong>
            </div>
          </div>
        </div>
      </ng-container>
      <ng-container *ngIf="activity.moduleType==='fundTransactions' && activity?.type!=='TOKENS'">
        <div class="d-flex align-items-center">
          <div class="timeline-label fw-bolder text-gray-800 fs-6">
            {{ activity.createdAt | date: 'h:mm a' }}
          </div>
          <div class="timeline-badge">
            <em class="fa fa-genderless fs-1"
              [ngClass]="{'text-warning': activity.moduleType === 'member', 'text-success': activity.moduleType === 'project', 'text-danger': activity.moduleType === 'project', 'text-info': activity.moduleType === 'post'}"></em>
          </div>
          <div class="timeline-content fw-normal text-gray-800 ps-3">
            <strong><a class="text-gray-800 text-hover-primary text-break"
                (click)="sharedService.doNavigation(activity,'relatedName')">{{activity?.moduleName
                || '
                '}}</a></strong>
            imported transactions in <strong><a class="text-gray-800 text-hover-primary text-break">{{
                cityData[activity.cityId] | titlecase }}</a></strong> city.
            <br />

            <div class="fw-normal fs-6 text-gray-400">
              By <strong> {{ activity.createdUserName }} </strong>
            </div>
          </div>
        </div>
      </ng-container>
      <ng-container *ngIf="activity?.type==='GAME'">
        <div class="d-flex align-items-center">
          <div class="timeline-label fw-bolder text-gray-800 fs-6">
            {{ activity.createdAt | date: 'h:mm a' }}
          </div>
          <div class="timeline-badge">
            <em class="fa fa-genderless fs-1"
              [ngClass]="{'text-warning': activity.moduleType === 'member', 'text-success': activity.moduleType === 'project', 'text-danger': activity.moduleType === 'project', 'text-info': activity.moduleType === 'post'}"></em>
          </div>
          <div class="timeline-content fw-normal text-gray-800 ps-3">
            <strong>
              <a class="text-gray-800 text-hover-primary">
                {{ activity.moduleName | titlecase }}
              </a>
            </strong>
            has logged into
            <strong> <a class="text-gray-800 text-hover-primary">
                {{ activity?.gameData?.gameName | titlecase }}
              </a></strong>.
            <br />

            <div class="fw-normal fs-6 text-gray-400">
              By <strong> {{ activity.moduleName }} </strong>
            </div>
          </div>
        </div>
      </ng-container>
    </div>
  </div>
  <div class="d-flex justify-content-center align-items-center text-gray-700 fw-semibold fs-6"
    *ngIf="activitiesList.length === 0">
    No logs!
  </div>
</div>
<ngx-spinner name="dashboard_activity_spinner" [fullScreen]="false" type="ball-clip-rotate" bdColor="white"
  size="medium" color="#354029"></ngx-spinner>