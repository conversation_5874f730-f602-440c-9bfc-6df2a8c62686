<ng-container *ngIf="activity">
  <!-- Router Link for most cases -->
  <a *ngIf="routerLink && !useClickHandler" 
     [routerLink]="routerLink" 
     [ngClass]="linkClasses">
    {{ displayText | titlecase }}
  </a>
  
  <!-- <PERSON><PERSON> Handler for special cases (association, update-profile) -->
  <a *ngIf="useClickHandler" 
     (click)="onLinkClick()" 
     [ngClass]="linkClasses"
     style="cursor: pointer;">
    {{ displayText | titlecase }}
  </a>
  
  <!-- Fallback for cases with no navigation -->
  <span *ngIf="!routerLink && !useClickHandler" 
        [ngClass]="linkClasses">
    {{ displayText | titlecase }}
  </span>
</ng-container>
