import { Component, Input, OnInit } from '@angular/core';

import { forkJoin } from 'rxjs';

import { SharedService } from '../../../../shared/services/shared.service';
import { ActivityObject } from '../../models/activities.model';
import { ActivitiesService } from '../../services/activities.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { Router } from '@angular/router';

@Component({
  selector: 'app-activities-widget',
  templateUrl: './activities-widget.component.html',
  styleUrls: ['./activities-widget.component.scss'],
})
export class ActivitiesWidgetComponent implements OnInit {
  @Input() limit: number;
  @Input() organizationId: string = '';

  activitiesList: any[] = [];
  cityData: { [k: string]: any; };

  constructor(
    public sharedService: SharedService,
    private readonly activitiesService: ActivitiesService,
    private readonly spinner: NgxSpinnerService,
    private readonly router: Router
  ) { }

  ngOnInit(): void {
    this.spinner.show('dashboard_activity_spinner');

    this.sharedService.defaultCityId.subscribe((data: any) => {
      if (data) {
        this.getActivitiesList();
      }
    });
    this.getAllCities();
  }

  /**
   * * Get Activities List
   * ? This function is used to fetch the activities list.
   */
  getActivitiesList(): void {
    forkJoin([
      this.activitiesService.getActivitiesList(),
      this.sharedService.getCognitoUsersList(),
    ]).subscribe({
      next: (response: any) => {

        this.activitiesList = response[0].data.activitiesByDate.items.filter(
          (element: any) =>
            element?.cityId === this.sharedService?.defaultCityId?.value && element?.type !== 'TOKENS'
        );

        // Use the createdUserName directly from the activity data
        this.activitiesList.forEach((activityObj: ActivityObject) => {
          // Keep the existing createdUserName if it exists, otherwise use the moduleUser's name
          if (!activityObj.createdUserName && activityObj.moduleUser?.name) {
            activityObj.createdUserName = activityObj.moduleUser.name;
          }
        });

        this.activitiesList = this.activitiesList.splice(0, 6);

        this.sharedService.isVillageNameChange.next(false);
        this.spinner.hide('dashboard_activity_spinner');
      },
      error: (error: Error) => {
        this.spinner.hide('dashboard_activity_spinner');
      },
    });
  }

  /**
   * * Navigate To Organization
   * ? This function is used to navigate to member's profile page.
   * @param data This parameter hold the activity data of the clicked activity.
   */
  navigateToOrganization(data: any): void {
    this.router.navigate([
      '/members/view-member',
      this.sharedService.getEncryptedId(data?.relatedId),
    ]);
  }

  /**
   * * Profile Navigation
   * ? This function is used to redirect to the url received in the parameter.
   * @param activity This parameter holds the data of the clicked activity 
   * @param url This parameter holds the destination for the redirect action.
   */
  profileNavigation(activity: any, url: string) {
    this.sharedService.activityId.next(activity.id);
    this.router.navigate([url]);
  }

  /**
   * * Get All Cities
   * ? This function is used for getting city data.
   */
  getAllCities() {
    this.sharedService.getAllCities().subscribe({
      next: ((response: any) => {
        this.cityData = Object.fromEntries(response.data.listCities.items.map((city: any) => [city.id, city.name]));
      })
    })
  }

}
