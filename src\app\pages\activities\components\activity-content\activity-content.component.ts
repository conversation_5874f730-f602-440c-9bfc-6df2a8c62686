import { Component, Input, Output, EventEmitter } from '@angular/core';
import { SharedService } from '../../../../shared/services/shared.service';

@Component({
  selector: 'app-activity-content',
  templateUrl: './activity-content.component.html',
  styleUrls: ['./activity-content.component.scss']
})
export class ActivityContentComponent {
  @Input() activity: any;
  @Input() cityData: any = {};
  @Output() profileNavigation = new EventEmitter<{activity: any, url: string}>();
  @Output() navigateToOrganization = new EventEmitter<any>();

  constructor(private sharedService: SharedService) {}

  get contentType(): string {
    if (!this.activity) return 'none';

    const { moduleType, type, relatedTo, isRelationship, activityType } = this.activity;

    // Special cases first
    if (type === 'GAME') return 'game';
    if (moduleType === 'homework-submission') return 'homework-submission';
    if (moduleType === 'update-profile') return 'update-profile';
    if (moduleType === 'fundTransactions' && type === 'ADDED') return 'fund-transactions';
    if (type === 'VIEWED') return 'viewed';
    if (type === 'TOKENS' && activityType === 'FUNDING') return 'funding-tokens';
    if (type === 'TOKENS') return 'tokens';

    // Funding cases
    if (moduleType === 'funding' && type !== 'TOKENS' && !isRelationship) return 'funding-donation';
    if (moduleType === 'funding' && type !== 'TOKENS' && isRelationship) return 'funding-disbursement';

    // Project relationship case
    if (moduleType === 'project' && isRelationship && type !== 'TOKENS') return 'project-relationship';

    // General relationship case
    if (relatedTo && relatedTo !== moduleType && 
        !(isRelationship && moduleType === 'project') && 
        !(type === 'DELETED' && this.activity.relatedId) && 
        (type !== 'VIEWED' && type !== 'TOKENS') && 
        moduleType !== 'funding' && 
        moduleType !== 'fundTransactions' && 
        type !== 'GAME') {
      return 'general-relationship';
    }

    // Standard activity case
    if ((!relatedTo && !(moduleType === 'project' && isRelationship) && 
         moduleType !== 'funding' && activityType !== 'FUNDING' && 
         moduleType !== 'fundTransactions') || 
        (type === 'DELETED' && this.activity.relatedId && type === 'GAME') || 
        (moduleType === 'association' && isRelationship && type === 'DELETED')) {
      return 'standard';
    }

    return 'none';
  }

  get relationshipActionText(): string {
    const { type, moduleType } = this.activity;
    
    if (type === 'UPDATED') return '<strong> assigned new role </strong> in ';
    if (type === 'ADDED') return '<strong> enrolled </strong> to ';
    if (moduleType === 'association' && type === 'DELETED') return '<strong> disassociated </strong> from ';
    return '<strong> discharged </strong> from ';
  }

  get projectActionText(): string {
    const { type } = this.activity;
    return type === 'ADDED' ? 'scheduled' : 'cancelled';
  }

  onProfileNavigation(url: string): void {
    this.profileNavigation.emit({ activity: this.activity, url });
  }

  onNavigateToOrganization(): void {
    this.navigateToOrganization.emit(this.activity);
  }

  onSharedNavigation(field?: string): void {
    this.sharedService.doNavigation(this.activity, field);
  }
}
