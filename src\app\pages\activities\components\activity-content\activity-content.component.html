<ng-container [ngSwitch]="contentType">
  
  <!-- Standard Activity Content -->
  <ng-container *ngSwitchCase="'standard'">
    <strong>
      <app-activity-link 
        [activity]="activity" 
        [linkText]="activity.moduleName"
        [useBreakClass]="true"
        (profileNavigation)="onProfileNavigation($event.url)">
      </app-activity-link>
    </strong>
    has been
    <strong *ngIf="(!activity.isRelationship && !activity.relatedTo) || (activity.moduleType!=='association' && activity.type === 'DELETED')">
      <app-activity-link 
        *ngIf="activity.moduleType === 'update-profile'"
        [activity]="activity" 
        [linkText]="activity.type | lowercase"
        (profileNavigation)="onProfileNavigation($event.url)">
      </app-activity-link>
      <span *ngIf="activity.moduleType !== 'update-profile'">
        {{ activity.type | lowercase }}
      </span>
    </strong>
    <span *ngIf="activity.isRelationship">
      <span [innerHTML]="relationshipActionText"></span>
      <strong>
        <app-activity-link 
          *ngIf="activity.moduleType === 'association'"
          [activity]="{moduleType: 'association', moduleId: activity.relatedId, moduleName: activity.relatedName}"
          [linkText]="activity.relatedName"
          (profileNavigation)="onSharedNavigation()">
        </app-activity-link>
        <app-activity-link 
          *ngIf="activity.moduleType !== 'association'"
          [activity]="{moduleType: activity.relatedTo, moduleId: activity.relatedId, moduleName: activity.relatedName}"
          [linkText]="activity.relatedName">
        </app-activity-link>
      </strong>
    </span>.
  </ng-container>

  <!-- General Relationship Content -->
  <ng-container *ngSwitchCase="'general-relationship'">
    Relationship between
    <strong>
      <a (click)="onSharedNavigation('relatedName')" class="text-gray-800 text-hover-primary">
        {{ activity.relatedName | titlecase }}
      </a>
    </strong>
    &
    <strong>
      <a (click)="onSharedNavigation('moduleName')" class="text-gray-800 text-hover-primary">
        {{ activity.moduleName | titlecase }}
      </a>
    </strong>
    has been
    <strong> {{ activity.type | lowercase }} </strong>.
  </ng-container>

  <!-- Funding Donation Content -->
  <ng-container *ngSwitchCase="'funding-donation'">
    The city
    <strong>
      <a class="text-gray-800 text-hover-primary">
        {{ activity.moduleName | titlecase }}
      </a>
    </strong>
    has received a donation.
  </ng-container>

  <!-- Funding Tokens Content -->
  <ng-container *ngSwitchCase="'funding-tokens'">
    <strong>
      <a class="text-gray-800 text-hover-primary">
        {{ activity?.relatedName | titlecase }}
      </a>
    </strong>
    has received
    <strong>
      <a class="text-gray-800 text-hover-primary">
        ${{ activity?.activityTokens}}
      </a>
    </strong>
    , invested by
    <strong>
      <a class="text-gray-800 text-hover-primary">
        {{ activity?.moduleName | titlecase }}
      </a>
    </strong>
    for project
    <strong>
      <a class="text-gray-800 text-hover-primary">
        {{ activity?.updatedData?.givenName | titlecase }}
      </a>
    </strong>.
  </ng-container>

  <!-- Funding Disbursement Content -->
  <ng-container *ngSwitchCase="'funding-disbursement'">
    The city
    <strong>
      <a class="text-gray-800 text-hover-primary">
        {{ activity.moduleName | titlecase }}
      </a>
    </strong>
    has disbursed funds to
    <strong>
      <a (click)="onSharedNavigation('relatedName')" class="text-gray-800 text-hover-primary">
        {{ activity.relatedName | titlecase }}
      </a>
    </strong>.
  </ng-container>

  <!-- Project Relationship Content -->
  <ng-container *ngSwitchCase="'project-relationship'">
    <strong>
      <app-activity-link 
        [activity]="activity" 
        [linkText]="activity.moduleName">
      </app-activity-link>
    </strong>
    has been
    <strong> {{ projectActionText }} </strong>
    for
    <strong>
      <a *ngIf="activity?.relatedTo === 'member'" 
         (click)="onNavigateToOrganization()" 
         class="text-gray-800 text-hover-primary">
        {{ activity.relatedName | titlecase }}
      </a>
      <app-activity-link 
        *ngIf="activity?.relatedTo !== 'member'"
        [activity]="{moduleType: activity.relatedTo, moduleId: activity.relatedId, moduleName: activity.relatedName}"
        [linkText]="activity.relatedName">
      </app-activity-link>
    </strong>.
  </ng-container>

  <!-- Fund Transactions Content -->
  <ng-container *ngSwitchCase="'fund-transactions'">
    <strong>
      <a class="text-gray-800 text-hover-primary">
        {{ activity.moduleName | titlecase }}
      </a>
    </strong>
    imported transactions in
    <strong>
      <a class="text-gray-800 text-hover-primary">
        {{ cityData[activity.cityId] | titlecase }}
      </a>
    </strong>
    city.
  </ng-container>

  <!-- Viewed Content -->
  <ng-container *ngSwitchCase="'viewed'">
    <strong>
      <app-activity-link 
        [activity]="{moduleType: 'activity-submission', moduleId: activity.moduleId, moduleName: activity.moduleName}"
        [linkText]="activity.moduleName">
      </app-activity-link>
    </strong>
    has been
    <strong> viewed </strong>
  </ng-container>

  <!-- Tokens Content -->
  <ng-container *ngSwitchCase="'tokens'">
    <strong>
      <label class="text-gray-800">
        {{activity?.activityTokens}} tokens
      </label>
    </strong>
    has been
    <strong> Added </strong>
    to
    <strong *ngIf="activity?.relatedTo !== 'member'">
      <app-activity-link 
        [activity]="{moduleType: activity.relatedTo, moduleId: activity.relatedId, moduleName: activity.relatedName}"
        [linkText]="activity?.relatedName">
      </app-activity-link>
    </strong>
    <strong *ngIf="activity?.relatedTo === 'member'">
      <app-activity-link 
        [activity]="{moduleType: 'member', moduleId: activity.relatedId, moduleName: activity.relatedName}"
        [linkText]="activity?.relatedName">
      </app-activity-link>
    </strong>
  </ng-container>

</ng-container>
