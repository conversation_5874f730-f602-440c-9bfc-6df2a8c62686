import { Component, Input, Output, EventEmitter } from '@angular/core';

@Component({
  selector: 'app-activity-item',
  templateUrl: './activity-item.component.html',
  styleUrls: ['./activity-item.component.scss']
})
export class ActivityItemComponent {
  @Input() activity: any;
  @Input() index: number = 0;
  @Input() layout: 'table' | 'timeline' = 'table';
  @Input() cityData: any = {};
  @Input() showAvatar: boolean = true;
  @Input() showMeta: boolean = true;
  @Input() showTimestamp: boolean = false;
  
  @Output() profileNavigation = new EventEmitter<{activity: any, url: string}>();
  @Output() navigateToOrganization = new EventEmitter<any>();

  get isSpecialCase(): boolean {
    if (!this.activity) return false;
    
    const { moduleType, type } = this.activity;
    return moduleType === 'homework-submission' || 
           moduleType === 'update-profile' || 
           type === 'GAME';
  }

  get avatarName(): string {
    if (!this.activity) return '';
    
    const { moduleType, moduleName, relatedName } = this.activity;
    
    if (moduleType === 'homework-submission') {
      return relatedName || '';
    }
    
    return moduleName || '';
  }

  get shouldShowUpdateDetails(): boolean {
    return this.activity?.moduleType === 'update-profile' && 
           this.activity?.updatedData && 
           this.activity?.moduleUser;
  }

  onProfileNavigation(event: {activity: any, url: string}): void {
    this.profileNavigation.emit(event);
  }

  onNavigateToOrganization(activity: any): void {
    this.navigateToOrganization.emit(activity);
  }
}
