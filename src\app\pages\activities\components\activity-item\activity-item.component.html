<ng-container *ngIf="activity">
  
  <!-- Table Layout -->
  <div *ngIf="layout === 'table'" class="d-flex align-items-center">
    <!-- Avatar -->
    <app-activity-avatar 
      *ngIf="showAvatar"
      [name]="avatarName"
      [index]="index">
    </app-activity-avatar>
    
    <!-- Content -->
    <div>
      <!-- Special Cases -->
      <ng-container *ngIf="isSpecialCase">
        
        <!-- Homework Submission -->
        <ng-container *ngIf="activity.moduleType === 'homework-submission'">
          <strong>
            <a class="text-gray-800 text-hover-primary text-break"
               (click)="onProfileNavigation({activity: activity, url: '/students/view-student/' + activity.relatedId})">
              {{ activity?.relatedName || '' }}
            </a>
          </strong>
          has submitted their homework 
          <strong>
            <app-activity-link 
              [activity]="{moduleType: 'activity-submission', moduleId: activity.moduleId, moduleName: activity.moduleName}"
              [linkText]="activity.moduleName"
              [useBreakClass]="true">
            </app-activity-link>
          </strong>.
        </ng-container>

        <!-- Update Profile -->
        <ng-container *ngIf="activity.moduleType === 'update-profile'">
          <strong>
            <app-activity-link 
              [activity]="activity"
              [linkText]="activity?.moduleName || ''"
              [useBreakClass]="true"
              (profileNavigation)="onProfileNavigation($event)">
            </app-activity-link>
          </strong>
          has requested 
          <strong>
            <app-activity-link 
              [activity]="activity"
              [linkText]="activity.relatedName"
              [useBreakClass]="true"
              (profileNavigation)="onProfileNavigation($event)">
            </app-activity-link>
          </strong>.

          <!-- Update Details -->
          <div *ngIf="shouldShowUpdateDetails" class="mt-2">
            <div class="mb-1">
              <span class="text-gray-600 fw-normal">Update:</span>
            </div>
            <ng-container *ngFor="let key of (activity?.updatedData | keyvalue)">
              <ng-container *ngIf="key.key !== 'id' && activity.moduleUser[key.key] !== key.value">
                <div class="fw-bold text-dark fs-8">
                  <span class="text-gray-800">{{ key.key | titlecase }}: </span>
                  <span class="text-gray-600">{{ key.value || '-' }}</span>
                </div>
              </ng-container>
            </ng-container>
          </div>
        </ng-container>

        <!-- Game Activity -->
        <ng-container *ngIf="activity?.type === 'GAME'">
          <strong>
            <a class="text-gray-800 text-hover-primary text-break">
              {{ activity?.moduleName || '' }}
            </a>
          </strong>
          <ng-container *ngIf="!activity?.gameData?.duration">
            has logged in to 
            <strong>
              <a class="text-gray-800 text-hover-primary text-break">
                {{ activity?.gameData?.gameName }}
              </a>
            </strong>.
          </ng-container>
          <ng-container *ngIf="activity?.gameData?.duration">
            took 
            <strong>
              <a class="text-gray-800 text-hover-primary text-break">
                {{ activity?.gameData?.duration }} seconds
              </a>
            </strong> 
            on a mission of 
            <strong>
              <a class="text-gray-800 text-hover-primary text-break">
                {{ activity?.gameData?.gameName || '' }}
              </a>
            </strong>.
          </ng-container>
        </ng-container>

      </ng-container>

      <!-- Standard Activity Content -->
      <ng-container *ngIf="!isSpecialCase">
        <app-activity-content 
          [activity]="activity"
          [cityData]="cityData"
          (profileNavigation)="onProfileNavigation($event)"
          (navigateToOrganization)="onNavigateToOrganization($event.activity)">
        </app-activity-content>
      </ng-container>

      <!-- Meta Information -->
      <br *ngIf="showMeta" />
      <app-activity-meta 
        *ngIf="showMeta"
        [activity]="activity"
        [showTimestamp]="showTimestamp"
        [layout]="'block'">
      </app-activity-meta>
    </div>
  </div>

  <!-- Timeline Layout -->
  <div *ngIf="layout === 'timeline'" class="timeline-item">
    <div class="timeline-label fw-bolder text-gray-800 fs-6">
      {{ activity.createdAt | date: 'h:mm a' }}
    </div>
    <div class="timeline-badge">
      <em class="fa fa-genderless fs-1"
          [ngClass]="{
            'text-warning': activity.moduleType === 'member', 
            'text-success': activity.moduleType === 'project', 
            'text-danger': activity.moduleType === 'project', 
            'text-info': activity.moduleType === 'post'
          }">
      </em>
    </div>
    <div class="timeline-content fw-normal text-gray-800 ps-3">
      
      <!-- Special Cases for Timeline -->
      <ng-container *ngIf="isSpecialCase">
        
        <!-- Homework Submission Timeline -->
        <ng-container *ngIf="activity.moduleType === 'homework-submission'">
          <strong>
            <a class="text-gray-800 text-hover-primary text-break"
               (click)="onProfileNavigation({activity: activity, url: '/students/view-student/' + activity.relatedId})">
              {{ activity?.relatedName || '' }}
            </a>
          </strong>
          has submitted their homework 
          <strong>
            <app-activity-link 
              [activity]="{moduleType: 'homework-submission', moduleId: activity.moduleId, moduleName: activity.moduleName}"
              [linkText]="activity.moduleName"
              [useBreakClass]="true">
            </app-activity-link>
          </strong>.
          <br />
          <app-activity-meta 
            [activity]="activity"
            [layout]="'block'">
          </app-activity-meta>
        </ng-container>

        <!-- Update Profile Timeline -->
        <ng-container *ngIf="activity.moduleType === 'update-profile'">
          <strong>
            <app-activity-link 
              [activity]="activity"
              [linkText]="activity?.moduleName || ''"
              [useBreakClass]="true"
              (profileNavigation)="onProfileNavigation($event)">
            </app-activity-link>
          </strong>
          has requested 
          <strong>
            <app-activity-link 
              [activity]="activity"
              [linkText]="activity.relatedName"
              [useBreakClass]="true"
              (profileNavigation)="onProfileNavigation($event)">
            </app-activity-link>
          </strong>.
          <br />
          <app-activity-meta 
            [activity]="activity"
            [layout]="'block'">
          </app-activity-meta>
        </ng-container>

        <!-- Game Timeline -->
        <ng-container *ngIf="activity?.type === 'GAME'">
          <strong>
            <a class="text-gray-800 text-hover-primary">
              {{ activity.moduleName | titlecase }}
            </a>
          </strong>
          has logged into
          <strong>
            <a class="text-gray-800 text-hover-primary">
              {{ activity?.gameData?.gameName | titlecase }}
            </a>
          </strong>.
          <br />
          <app-activity-meta 
            [activity]="{createdUserName: activity.moduleName}"
            [layout]="'block'">
          </app-activity-meta>
        </ng-container>

      </ng-container>

      <!-- Standard Timeline Content -->
      <ng-container *ngIf="!isSpecialCase">
        <app-activity-content 
          [activity]="activity"
          [cityData]="cityData"
          (profileNavigation)="onProfileNavigation($event)"
          (navigateToOrganization)="onNavigateToOrganization($event.activity)">
        </app-activity-content>
        <app-activity-meta 
          [activity]="activity"
          [layout]="'inline'">
        </app-activity-meta>.
      </ng-container>
      
    </div>
  </div>

</ng-container>
